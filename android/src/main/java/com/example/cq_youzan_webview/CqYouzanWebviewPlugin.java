package com.example.cq_youzan_webview;

import android.content.Context;
import android.util.Log;
import androidx.annotation.NonNull;

import com.youzan.androidsdk.InitConfig;
import com.youzan.androidsdk.YouzanToken;
import com.youzan.androidsdk.YzLoginCallback;
import com.youzan.androidsdkx5.YouZanSDKX5Adapter;
import com.youzan.androidsdk.YouzanSDK;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import io.flutter.plugin.platform.PlatformViewRegistry;

import java.util.Map;

/** CqYouzanWebviewPlugin */
public class CqYouzanWebviewPlugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
  private static final String TAG = "CqYouzanWebviewPlugin";
  private static final String PLATFORM_VIEW_TYPE = "plugins.cq.platform_youzan_webview";

  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity
  private MethodChannel channel;
  private Context context;

  @Override
  public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
    // 注册全局方法通道
    channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "plugin.cq.youzan_webview_native_channel");
    channel.setMethodCallHandler(this);

    // 注册PlatformView
    flutterPluginBinding
        .getPlatformViewRegistry()
        .registerViewFactory(PLATFORM_VIEW_TYPE, new YouzanWebViewFactory(flutterPluginBinding.getBinaryMessenger()));
  }

  @Override
  public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
    switch (call.method) {
      case "initYZSdk":
        handleInitYZSdk(call, result);
        break;
      case "login":
        handleLogin(call, result);
        break;
      case "logout":
        handleLogout(result);
        break;
      case "getPlatformVersion":
        result.success("Android " + android.os.Build.VERSION.RELEASE);
        break;
      default:
        result.notImplemented();
        break;
    }
  }

  private void handleInitYZSdk(@NonNull MethodCall call, @NonNull Result result) {
    try {
      Map<String, Object> arguments = (Map<String, Object>) call.arguments;
      String clientId = (String) arguments.get("clientId");
      String appKey = (String) arguments.get("appKey");

      if (clientId == null || appKey == null) {
        result.error("INVALID_PARAMS", "clientId and appKey cannot be null", null);
        return;
      }

      if (context == null) {
        result.error("NO_CONTEXT", "Context is not available", null);
        return;
      }

      InitConfig initConfig = InitConfig.builder()
          .clientId(clientId)
          .appkey(appKey)
          .adapter(new YouZanSDKX5Adapter())
          .initCallBack((b, s) -> {
            Log.e(TAG, "onCreate: 初始化回调：" + b + " " + s);
          }).advanceHideX5Loading(false)
          .build();

      YouzanSDK.init(context, initConfig);
      result.success(null);

    } catch (Exception e) {
      Log.e(TAG, "initYZSdk error: " + e.getMessage());
      result.error("INIT_ERROR", "Failed to initialize YouzanSDK: " + e.getMessage(), null);
    }
  }

  private void handleLogin(@NonNull MethodCall call, @NonNull Result result) {
    try {
      Map<String, Object> arguments = (Map<String, Object>) call.arguments;
      String userId = (String) arguments.get("userId");
      String avatar = (String) arguments.get("avatar");
      String extra = (String) arguments.get("extra");
      String nickName = (String) arguments.get("nickName");
      Integer gender = (Integer) arguments.get("gender");

      if (userId == null) {
        result.error("INVALID_PARAMS", "userId cannot be null", null);
        return;
      }

      // 设置默认值
      if (avatar == null) avatar = "";
      if (extra == null) extra = "";
      if (nickName == null) nickName = "";
      if (gender == null) gender = 0;

      YouzanSDK.yzlogin(userId, avatar, extra, nickName, gender.toString(), new YzLoginCallback() {
        @Override
        public void onSuccess(YouzanToken youzanToken) {
          Log.e(TAG, "login onSuccess: " + youzanToken.toString());
          // 同步token到所有webview实例
          YouzanWebViewController.syncTokenToAllWebViews(youzanToken);
          result.success(null);
        }

        @Override
        public void onFail(String s) {
          Log.e(TAG, "login onFail: " + s);
          result.error("LOGIN_FAILED", "Login failed: " + s, null);
        }
      });

    } catch (Exception e) {
      Log.e(TAG, "login error: " + e.getMessage());
      result.error("LOGIN_ERROR", "Failed to login: " + e.getMessage(), null);
    }
  }

  private void handleLogout(@NonNull Result result) {
    try {
      if (context == null) {
        result.error("NO_CONTEXT", "Context is not available", null);
        return;
      }

      YouzanSDK.userLogout(context);
      result.success(null);

    } catch (Exception e) {
      Log.e(TAG, "logout error: " + e.getMessage());
      result.error("LOGOUT_ERROR", "Failed to logout: " + e.getMessage(), null);
    }
  }

  // ActivityAware interface implementation
  @Override
  public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
    this.context = binding.getActivity();
  }

  @Override
  public void onDetachedFromActivityForConfigChanges() {
    // Keep context for config changes
  }

  @Override
  public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
    this.context = binding.getActivity();
  }

  @Override
  public void onDetachedFromActivity() {
    this.context = null;
  }

  @Override
  public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
    channel.setMethodCallHandler(null);
  }
}
