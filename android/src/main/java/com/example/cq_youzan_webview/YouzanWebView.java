package com.example.cq_youzan_webview;

import android.content.Context;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.youzan.androidsdkx5.YouzanBrowser;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.platform.PlatformView;

public class YouzanWebView implements PlatformView {
    private final YouzanBrowser youzanBrowser;
    private final YouzanWebViewController controller;

    public YouzanWebView(@NonNull Context context, int viewId, @NonNull BinaryMessenger messenger) {
        // 创建YouzanBrowser实例
        youzanBrowser = new YouzanBrowser(context);
        
        // 创建控制器来处理MethodChannel通信
        controller = new YouzanWebViewController(viewId, messenger, youzanBrowser);
    }

    @NonNull
    @Override
    public View getView() {
        return youzanBrowser;
    }

    @Override
    public void dispose() {
        if (controller != null) {
            controller.dispose();
        }
        if (youzanBrowser != null) {
            youzanBrowser.destroy();
        }
    }
}
