package com.example.cq_youzan_webview;

import androidx.annotation.NonNull;

import com.youzan.androidsdk.YouzanToken;
import com.youzan.androidsdkx5.YouzanBrowser;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class YouzanWebViewController implements MethodCallHandler {
    private static final ConcurrentHashMap<Integer, YouzanWebViewController> instances = new ConcurrentHashMap<>();

    private final int viewId;
    private final MethodChannel methodChannel;
    private final YouzanBrowser youzanBrowser;

    public YouzanWebViewController(int viewId, BinaryMessenger messenger, YouzanBrowser youzanBrowser) {
        this.viewId = viewId;
        this.youzanBrowser = youzanBrowser;
        this.methodChannel = new MethodChannel(messenger, "plugin.cq.youzan_webview_controller_channel_" + viewId);
        this.methodChannel.setMethodCallHandler(this);

        // 注册实例
        instances.put(viewId, this);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        switch (call.method) {
            case "loadUrl":
                handleLoadUrl(call, result);
                break;
            case "canGoBack":
                handleCanGoBack(result);
                break;
            case "goBack":
                handleGoBack(result);
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    private void handleLoadUrl(@NonNull MethodCall call, @NonNull Result result) {
        try {
            Map<String, Object> arguments = (Map<String, Object>) call.arguments;
            String url = (String) arguments.get("url");
            
            if (url != null && !url.isEmpty()) {
                youzanBrowser.loadUrl(url);
                result.success(null);
            } else {
                result.error("INVALID_URL", "URL cannot be null or empty", null);
            }
        } catch (Exception e) {
            result.error("LOAD_URL_ERROR", "Failed to load URL: " + e.getMessage(), null);
        }
    }

    private void handleCanGoBack(@NonNull Result result) {
        try {
            boolean canGoBack = youzanBrowser.canGoBack();
            result.success(canGoBack);
        } catch (Exception e) {
            result.error("CAN_GO_BACK_ERROR", "Failed to check canGoBack: " + e.getMessage(), null);
        }
    }

    private void handleGoBack(@NonNull Result result) {
        try {
            if (youzanBrowser.canGoBack()) {
                youzanBrowser.goBack();
                result.success(null);
            } else {
                result.error("CANNOT_GO_BACK", "Cannot go back", null);
            }
        } catch (Exception e) {
            result.error("GO_BACK_ERROR", "Failed to go back: " + e.getMessage(), null);
        }
    }

    public void dispose() {
        // 移除实例
        instances.remove(viewId);

        if (methodChannel != null) {
            methodChannel.setMethodCallHandler(null);
        }
    }

    // 静态方法：同步token到所有webview实例
    public static void syncTokenToAllWebViews(YouzanToken token) {
        for (YouzanWebViewController controller : instances.values()) {
            if (controller.youzanBrowser != null) {
                // 确保WebView操作在主线程中执行
                controller.youzanBrowser.post(() -> controller.youzanBrowser.sync(token));
            }
        }
    }
}
